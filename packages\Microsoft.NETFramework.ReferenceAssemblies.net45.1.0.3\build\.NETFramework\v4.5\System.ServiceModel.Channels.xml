﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ServiceModel.Channels</name>
  </assembly>
  <members>
    <member name="T:System.ServiceModel.UdpBinding">
      <summary>Represents a UDP binding.</summary>
    </member>
    <member name="M:System.ServiceModel.UdpBinding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.UdpBinding" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.UdpBinding.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.UdpBinding" /> class with a specified configuration name.</summary>
      <param name="configurationName">The configuration name.</param>
    </member>
    <member name="M:System.ServiceModel.UdpBinding.CreateBindingElements">
      <summary>Creates a collection of binding elements.</summary>
      <returns>A collection of binding elements.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.DuplicateMessageHistoryLength">
      <summary>Gets or sets the value of the duplicate message history length in a UDP binding element.</summary>
      <returns>The maximum number of message hashes used by the binding to identify duplicate messages.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.MaxBufferPoolSize">
      <summary>Gets or sets a maximum buffer pool size associated with the binding.</summary>
      <returns>A number that indicates the size of a buffer pool.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.MaxPendingMessagesTotalSize">
      <summary>Gets or sets the maximum number of messages that are received but not yet removed from the input queue for an individual channel instance.</summary>
      <returns>The maximum number of pending messages.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.MaxReceivedMessageSize">
      <summary>Gets or sets the maximum size for a message that can be processed by the binding.</summary>
      <returns>The maximum size for a message that can be processed by the binding.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.MaxRetransmitCount">
      <summary>Gets or sets the maximum number of retransmit messages in a UDP binding.</summary>
      <returns>The maximum number of retransmit messages in a UDP binding.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.MulticastInterfaceId">
      <summary>Gets or sets the value of the MulticastInterfaceID property in a UDP binding.</summary>
      <returns>The network adapter that is used when sending and receiving multicast traffic on multi-honed machines.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.ReaderQuotas">
      <summary>Gets or sets the reader quotas.</summary>
      <returns>The reader quotas.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.Scheme">
      <summary>Gets the scheme name for this binding.</summary>
      <returns>The scheme name for this binding.</returns>
    </member>
    <member name="M:System.ServiceModel.UdpBinding.ShouldSerializeReaderQuotas">
      <summary>Returns a value that indicates whether the ReaderQuotas should be serialized.</summary>
      <returns>True if the ReaderQuotas should be serialized; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.UdpBinding.ShouldSerializeTextEncoding">
      <summary>Returns whether settings for text encoding should be serialized.</summary>
      <returns>True if the settings for text encoding should be serialized; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.System#ServiceModel#Channels#IBindingRuntimePreferences#ReceiveSynchronously">
      <summary>Gets a value that indicates whether incoming requests are handled synchronously or asynchronously.</summary>
      <returns>True if incoming requests are handled synchronously; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.TextEncoding">
      <summary>Gets or sets the text encoding that is used for the binding.</summary>
      <returns>The text encoding.</returns>
    </member>
    <member name="P:System.ServiceModel.UdpBinding.TimeToLive">
      <summary>Gets or sets the value of the TimeToLive property in a UDP binding.</summary>
      <returns>The number of network segment hops that a multicast packet can traverse.</returns>
    </member>
    <member name="T:System.ServiceModel.Channels.ByteStreamMessage">
      <summary>Represents a byte stream message.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessage.CreateMessage(System.ArraySegment{System.Byte})">
      <summary>Creates a byte stream message using the specified buffer data.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.Message" /> object for the message created.</returns>
      <param name="buffer">The buffer that provides data for the message to be created.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessage.CreateMessage(System.ArraySegment{System.Byte},System.ServiceModel.Channels.BufferManager)">
      <summary>Creates a byte stream message using the specified buffer data and buffer manager.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.Message" /> object for the message created.</returns>
      <param name="buffer">The buffer that provides data for the message to be created.</param>
      <param name="bufferManager">The <see cref="T:System.ServiceModel.Channels.BufferManager" /> that manages the buffer to which the message is created.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessage.CreateMessage(System.IO.Stream)">
      <summary>Creates a byte stream message using the specified stream.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.Message" /> object for the message created.</returns>
      <param name="stream">The stream used to create the message.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement">
      <summary>The binding element that specifies the message encoding as a stream of bytes and has the option to specify the character encoding.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.#ctor(System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> class with the specified quota.</summary>
      <param name="quota">The constraints on the complexity of XML messages that can be processed by endpoints configured with this binding element.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.BuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel factory stack on the client that creates a specified type of channel for a specified context.</summary>
      <returns>A channel factory of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The binding context for the channel.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.BuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Builds the channel listener on the service that accepts a specified type of channel for a specified context.</summary>
      <returns>A channel listener of type <paramref name="TChannel" /> for the specified context.</returns>
      <param name="context">The binding context for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CanBuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the current binding can build a factory for a specified type of channel and context.</summary>
      <returns>true if the specified channel factory stack can be built on the service; otherwise, false.</returns>
      <param name="context">The binding context for the channel.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CanBuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the current binding can build a listener for a specified type of channel and context.</summary>
      <returns>true if the specified channel listener stack can be built on the service; otherwise, false.</returns>
      <param name="context">The binding context for the listener.</param>
      <typeparam name="TChannel">The type of channel the channel listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.Clone">
      <summary>Creates a new <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" /> object initialized from the current one.</summary>
      <returns>A binding element with property values equal to those of the current element.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.CreateMessageEncoderFactory">
      <summary>Creates a factory for byte stream message encoders that employ the SOAP and WS-Addressing versions and the character encoding specified by the current encoding binding element.</summary>
      <returns>The message encoder factory that this binding element creates.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.MessageVersion">
      <summary>Gets or sets the SOAP message and WS-Addressing versions that are used or expected.</summary>
      <returns>The message version that is used or expected.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas">
      <summary>Gets or sets constraints on the complexity of XML messages that can be processed by endpoints configured with this binding element.</summary>
      <returns>The complexity constraints on SOAP messages exchanged. The default values for these constraints are provided in the following remarks section.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ShouldSerializeMessageVersion">
      <summary>Returns a value that indicates whether the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.MessageVersion" /> property has changed from its default value and should be serialized.</summary>
      <returns>false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ShouldSerializeReaderQuotas">
      <summary>Returns a value that indicates whether the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas" /> property has changed from its default value and should be serialized.</summary>
      <returns>true if the <see cref="P:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement.ReaderQuotas" /> property value should be serialized; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.HttpRequestMessageExtensionMethods.ToMessage(System.Net.Http.HttpRequestMessage)">
      <summary> Creates a <see cref="T:System.ServiceModel.Channels.Message" /> instance from an <see cref="T:System.Net.Http.HttpRequestMessage" /> instance.</summary>
      <returns>The new <see cref="T:System.ServiceModel.Channels.Message" /> instance.</returns>
      <param name="httpRequestMessage">The <see cref="T:System.Net.Http.HttpRequestMessage" /> from which to create the <see cref="T:System.ServiceModel.Channels.Message" /> instance.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.HttpResponseMessageExtensionMethods">
      <summary> A static extension methods class for getting a <see cref="T:System.ServiceModel.Channels.Message" /> instance from an <see cref="T:System.Net.Http.HttpResponseMessage" /> instance. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.HttpResponseMessageExtensionMethods.ToMessage(System.Net.Http.HttpResponseMessage)">
      <summary> Creates a <see cref="T:System.ServiceModel.Channels.Message" /> instance from an <see cref="T:System.Net.Http.HttpResponseMessage" /> instance. </summary>
      <returns>The new <see cref="T:System.ServiceModel.Channels.Message" /> instance.</returns>
      <param name="httpResponseMessage">The <see cref="T:System.Net.Http.HttpResponseMessage" /> from which to create the <see cref="T:System.ServiceModel.Channels.Message" /> instance.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.MessageExtensionMethods">
      <summary> A static extension methods class for getting either an <see cref="T:System.Net.Http.HttpRequestMessage" /> or <see cref="T:System.Net.Http.HttpResponseMessage" /> instance from a <see cref="T:System.ServiceModel.Channels.Message" /> instance. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.MessageExtensionMethods.ToHttpRequestMessage(System.ServiceModel.Channels.Message)">
      <summary> Creates a <see cref="T:System.Net.Http.HttpRequestMessage" /> instance from an <see cref="T:System.ServiceModel.Channels.Message" /> instance. </summary>
      <returns>The <see cref="T:System.Net.Http.HttpRequestMessage" /> instance.</returns>
      <param name="message">The <see cref="T:System.ServiceModel.Channels.Message" /> instance from which to get the <see cref="T:System.Net.Http.HttpRequestMessage" /> instance.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.MessageExtensionMethods.ToHttpResponseMessage(System.ServiceModel.Channels.Message)">
      <summary> Creates a <see cref="T:System.Net.Http.HttpResponseMessage" /> instance from an <see cref="T:System.ServiceModel.Channels.Message" /> instance. </summary>
      <returns>The <see cref="T:System.Net.Http.HttpResponseMessage" /> instance.</returns>
      <param name="message">The <see cref="T:System.ServiceModel.Channels.Message" /> instance from which to get the <see cref="T:System.Net.Http.HttpResponseMessage" /> instance.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.NetworkInterfaceMessageProperty">
      <summary>Represents a network interface message property.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.NetworkInterfaceMessageProperty" /> class using a specified interfaceIndex.</summary>
      <param name="interfaceIndex">The specified interface index.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.AddTo(System.ServiceModel.Channels.Message)">
      <summary>Specifies a message to add into the message properties.</summary>
      <param name="message">The message to add.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.AddTo(System.ServiceModel.Channels.MessageProperties)">
      <summary>Specifies a property to add into the message properties.</summary>
      <param name="properties">The properties to add.</param>
    </member>
    <member name="P:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.InterfaceIndex">
      <summary>Gets the value that specifies the interface index for the message property.</summary>
      <returns>The value that specifies the interface index for the message property.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.Name">
      <summary>Gets the specified name for the network interface message property.</summary>
      <returns>The specified name for the network interface message property.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.TryGet(System.ServiceModel.Channels.Message,System.ServiceModel.Channels.NetworkInterfaceMessageProperty@)">
      <summary>Attempts to get the NetworkInterfaceMessageProperty from the properties of a specified message.</summary>
      <returns>True when a value for the NetworkInterfaceMessageproperty is obtained from the message; otherwise, false.</returns>
      <param name="message">The message from which the network interface message properties are to be retrieved.</param>
      <param name="property">The method returns true, networkInterfaceMessageProperty contains a NetworkInterfaceMessageProperty that is associated with the message.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.NetworkInterfaceMessageProperty.TryGet(System.ServiceModel.Channels.MessageProperties,System.ServiceModel.Channels.NetworkInterfaceMessageProperty@)">
      <summary>Attempts to get the NetworkInterfaceMessageProperty from a specified collection of message properties.</summary>
      <returns>True when a value for the NetworkInterfaceMessageProperty is obtained from the properties; otherwise, false.</returns>
      <param name="properties">The MessageProperties from which the network interfaces message properties are to be retrieved.</param>
      <param name="property">The method returns true, networkInterfaceMessageProperty contains a NetworkInterfaceMessageProperty that is associated with the properties.</param>
    </member>
    <member name="T:System.ServiceModel.Channels.UdpRetransmissionSettings">
      <summary>Specifies UDP retransmission settings.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.UdpRetransmissionSettings" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.UdpRetransmissionSettings" /> class with specified maximum unicast retransmit count and maximum multicast retransmit count.</summary>
      <param name="maxUnicastRetransmitCount">The maximum unicast retransmit count.</param>
      <param name="maxMulticastRetransmitCount">The maximum multicast retransmit count.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.#ctor(System.Int32,System.Int32,System.TimeSpan,System.TimeSpan,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.UdpRetransmissionSettings" /> class with specified maximum unicast retransmit count and maximum multicast retransmit count, delay lower bound, delay upper bound, and maximum delay per retransmission.</summary>
      <param name="maxUnicastRetransmitCount">The maximum unicast retransmit count.</param>
      <param name="maxMulticastRetransmitCount">The maximum multicast retransmit count.</param>
      <param name="delayLowerBound">The lower bound delay.</param>
      <param name="delayUpperBound">The upper bound delay.</param>
      <param name="maxDelayPerRetransmission">The maximum delay per retransmission.</param>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpRetransmissionSettings.DelayLowerBound">
      <summary>Gets or sets the delay of the lower bound for the retransmission setting.</summary>
      <returns>The delay of the lower bound for the retransmission setting.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpRetransmissionSettings.DelayUpperBound">
      <summary>Gets or sets the delay of the upper bound for the retransmission setting.</summary>
      <returns>The delay of the upper bound for the retransmission setting.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpRetransmissionSettings.MaxDelayPerRetransmission">
      <summary>Gets or sets the maximum delay per retransmission for the retransmission setting.</summary>
      <returns>The maximum delay per retransmission for the retransmission setting.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpRetransmissionSettings.MaxMulticastRetransmitCount">
      <summary>Gets or sets the maximum multicast retransmission count.</summary>
      <returns>The maximum multicast retransmission count.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpRetransmissionSettings.MaxUnicastRetransmitCount">
      <summary>Gets or sets the maximum unicast retransmission count.</summary>
      <returns>The maximum unicast retransmission count.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.ShouldSerializeDelayLowerBound">
      <summary>Indicates whether the UDP retransmission settings should serialize the lower bound delay.</summary>
      <returns>True if the UDP retransmission settings should serialize the lower bound delay; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.ShouldSerializeDelayUpperBound">
      <summary>Indicates whether the UDP retransmission settings should serialize the upper bound delay.</summary>
      <returns>True if the UDP retransmission settings should serialize the upper bound delay; otherwise, false.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpRetransmissionSettings.ShouldSerializeMaxDelayPerRetransmission">
      <summary>Indicates whether the UDP retransmission settings should serialize the maximum delay per retransmission.</summary>
      <returns>True if the UDP retransmission settings should serialize the maximum delay per retransmission; otherwise, false.</returns>
    </member>
    <member name="T:System.ServiceModel.Channels.UdpTransportBindingElement">
      <summary>Represents a UDP transport binding element. </summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.UdpTransportBindingElement" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.BuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Initializes a channel factory for producing channels of a specified type from the binding context.</summary>
      <returns>The<see cref="T:System.ServiceModel.Channels.IChannelFactory`1" /> of type <paramref name="TChannel" /> initialized from the context.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> that provides context for the binding element.</param>
      <typeparam name="TChannel">The type of channel the factory builds.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.BuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Initializes a channel listener to accept channels of a specified type from the binding context.</summary>
      <returns>The <see cref="T:System.ServiceModel.Channels.IChannelListener`1" /> of type <paramref name="TChannel" /> initialized from the context.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> that provides context for the binding element.</param>
      <typeparam name="TChannel">The type of channel the listener is built to accept.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.CanBuildChannelFactory``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the binding element can build a channel factory for a specific type of channel.</summary>
      <returns>true if the <see cref="T:System.ServiceModel.Channels.IChannelFactory`1" /> of type <paramref name="TChannel" /> can be built by the binding element; otherwise, false.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> the provides context for the binding element.</param>
      <typeparam name="TChannel">The type of channel the channel factory produces.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.CanBuildChannelListener``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a value that indicates whether the binding element can build a listener for a specific type of channel.</summary>
      <returns>true if the <see cref="T:System.ServiceModel.Channels.IChannelListener`1" /> of type <paramref name="TChannel" /> can be built by the binding element; otherwise, false.</returns>
      <param name="context">The <see cref="T:System.ServiceModel.Channels.BindingContext" /> the provides context for the binding element.</param>
      <typeparam name="TChannel">The type of channel the listener accepts.</typeparam>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.Clone">
      <summary>Returns a copy of the binding element object.</summary>
      <returns>A <see cref="T:System.ServiceModel.Channels.BindingElement" /> object that is a deep clone of the original.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.DuplicateMessageHistoryLength">
      <summary>Gets or sets the maximum number of message hashes used by the binding to identify duplicate messages.</summary>
      <returns>The maximum number of message hashes used by the binding to identify duplicate messages.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.GetProperty``1(System.ServiceModel.Channels.BindingContext)">
      <summary>Returns a property from the specified binding context.</summary>
      <returns>The property from the specified binding context.</returns>
      <param name="context">A binding context.</param>
      <typeparam name="T">The type of the property to get.</typeparam>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.MaxPendingMessagesTotalSize">
      <summary>Gets or sets the maximum number of messages that are received but not yet removed from the input queue for an individual channel instance.</summary>
      <returns>The maximum number of messages that are received but not yet removed from the input queue for an individual channel instance.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.MulticastInterfaceId">
      <summary>Gets or sets the network adapter that is used when sending and receiving multicast traffic on multi-honed machines.</summary>
      <returns>The network adapter that is used when sending and receiving multicast traffic on multi-honed machines.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.RetransmissionSettings">
      <summary>Gets or sets the UDP retransmission settings.</summary>
      <returns>The UDP retransmission settings.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.Scheme">
      <summary>Gets the URI scheme for the transport.</summary>
      <returns>The URI scheme for the transport.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.ShouldSerializeRetransmissionSettings">
      <summary>Indicates whether the retransmission settings from the binding element should serialize.</summary>
      <returns>true if the retransmission settings from the binding element should serialize; otherwise, false.</returns>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.SocketReceiveBufferSize">
      <summary>Gets or sets the receive buffer size on the underlying socket.</summary>
      <returns>The receive buffer size on the underlying socket.</returns>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.System#ServiceModel#Description#IPolicyExportExtension#ExportPolicy(System.ServiceModel.Description.MetadataExporter,System.ServiceModel.Description.PolicyConversionContext)">
      <summary>Writes the custom policy assertions into the context of the binding element.</summary>
      <param name="exporter">The object that exports the metadata information.</param>
      <param name="context">The <see cref="T:System.ServiceModel.Description.PolicyConversionContext" /> that you can use to insert the custom policy assertion.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.System#ServiceModel#Description#IWsdlExportExtension#ExportContract(System.ServiceModel.Description.WsdlExporter,System.ServiceModel.Description.WsdlContractConversionContext)">
      <summary>Writes the custom Web Services Description Language (WSDL) elements into the generated WSDL for a contract.</summary>
      <param name="exporter">The object that exports the contract information.</param>
      <param name="context">The context that provides mappings from exported WSDL elements to the contract description.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportBindingElement.System#ServiceModel#Description#IWsdlExportExtension#ExportEndpoint(System.ServiceModel.Description.WsdlExporter,System.ServiceModel.Description.WsdlEndpointConversionContext)">
      <summary>Writes the custom Web Services Description Language (WSDL) elements into the generated WSDL for an endpoint.</summary>
      <param name="exporter">The object that exports the contract information.</param>
      <param name="context">The context that provides mappings from exported WSDL elements to the endpoint description.</param>
    </member>
    <member name="P:System.ServiceModel.Channels.UdpTransportBindingElement.TimeToLive">
      <summary>Gets or sets the number of network segment hops.</summary>
      <returns>The number of network segment hops.</returns>
    </member>
    <member name="T:System.ServiceModel.Channels.UdpTransportImporter">
      <summary>Represents a UDP transport importer.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportImporter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Channels.UdpTransportImporter" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportImporter.BeforeImport(System.Web.Services.Description.ServiceDescriptionCollection,System.Xml.Schema.XmlSchemaSet,System.Collections.Generic.ICollection{System.Xml.XmlElement})">
      <summary>Represents the UDP transport prior to importing metadata documents.</summary>
      <param name="wsdlDocuments">The WSDL documents.</param>
      <param name="xmlSchemas">The XML schemas.</param>
      <param name="policy">The policy.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportImporter.ImportContract(System.ServiceModel.Description.WsdlImporter,System.ServiceModel.Description.WsdlContractConversionContext)">
      <summary>Imports contract to the UDP transport importer.</summary>
      <param name="importer">The WSDL importer.</param>
      <param name="context">The context.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportImporter.ImportEndpoint(System.ServiceModel.Description.WsdlImporter,System.ServiceModel.Description.WsdlEndpointConversionContext)">
      <summary>Imports endpoint to the UDP transport importer.</summary>
      <param name="importer">The WSDL importer.</param>
      <param name="context">The context.</param>
    </member>
    <member name="M:System.ServiceModel.Channels.UdpTransportImporter.ImportPolicy(System.ServiceModel.Description.MetadataImporter,System.ServiceModel.Description.PolicyConversionContext)">
      <summary>Imports policy to the UDP transport importer.</summary>
      <param name="importer">The WSDL importer.</param>
      <param name="context">The context.</param>
    </member>
    <member name="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement">
      <summary>Enables construction of a configuration element that defines the settings for a byte stream message encoding element.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> class. </summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.ApplyConfiguration(System.ServiceModel.Channels.BindingElement)">
      <summary>Applies the content of a specified binding element to the current <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> object.</summary>
      <param name="bindingElement">The binding element whose settings are to be applied to this configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.BindingElementType">
      <summary>Gets the type of the <see cref="T:System.ServiceModel.Channels.ByteStreamMessageEncodingBindingElement" />.</summary>
      <returns>A configuration element type.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.CopyFrom(System.ServiceModel.Configuration.ServiceModelExtensionElement)">
      <summary>Copies the content from the specified configuration section to the current <see cref="T:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement" /> object.</summary>
      <param name="from">The content being copied.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.ByteStreamMessageEncodingElement.ReaderQuotas">
      <summary>Gets a <see cref="T:System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement" /> configuration element that defines the constraints on the complexity of SOAP messages, which can be processed by endpoints configured with this encoding.</summary>
      <returns>A configuration element.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.UdpBindingCollectionElement">
      <summary>Represents a UDP binding collection element.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpBindingCollectionElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.UdpBindingCollectionElement" /> class.</summary>
    </member>
    <member name="T:System.ServiceModel.Configuration.UdpBindingElement">
      <summary>Represents a UDP binding element.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpBindingElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.UdpBindingElement" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpBindingElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.UdpBindingElement" /> class with specified name.</summary>
      <param name="name">The name.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.BindingElementType">
      <summary>Gets the type of the binding.</summary>
      <returns>The type of the binding.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.DuplicateMessageHistoryLength">
      <summary>Gets or sets the value of the maximum number of message hashes used in a UDP transport configuration element.</summary>
      <returns>The value of the maximum number of message hashes used.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpBindingElement.InitializeFrom(System.ServiceModel.Channels.Binding)">
      <summary>Initializes this binding configuration element with the content of the specified binding.</summary>
      <param name="binding">The binding used.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.MaxBufferPoolSize">
      <summary>Gets or sets the maximum size of any buffer pools used by the transport.</summary>
      <returns>The maximum size of any buffer pools used.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.MaxPendingMessagesTotalSize">
      <summary>Gets or sets a value that specifies the total size of messages that have been received but not yet removed from the property in a UDP binding element.</summary>
      <returns>The total size of pending messages.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.MaxReceivedMessageSize">
      <summary>Gets or sets the value of the maximum received message size property in a UDP binding element.</summary>
      <returns>The value of the maximum received message size property.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.MaxRetransmitCount">
      <summary>Gets or sets the maximum retransmit count for the UDP binding element.</summary>
      <returns>The maximum retransmit count.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.MulticastInterfaceId">
      <summary>Gets or sets the value of the network adapter that is used in a UDP binding element.</summary>
      <returns>The value of the network adapter that is used in a UDP binding element.</returns>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpBindingElement.OnApplyConfiguration(System.ServiceModel.Channels.Binding)">
      <summary>Applies the specified binding to the <see cref="T:System.ServiceModel.Configuration.UdpBindingElement" /> instance.</summary>
      <param name="binding">The binding to be applied.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.Properties">
      <summary>Gets the properties associated with the UDP binding element.</summary>
      <returns>The properties associated with the UDP binding element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.ReaderQuotas">
      <summary>Gets the reader quotas used in the UDP binding element.</summary>
      <returns>The reader quotas used in the UDP binding element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.TextEncoding">
      <summary>Gets or sets the text encoding used in the UDP binding element.</summary>
      <returns>The text encoding used in the UDP binding element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpBindingElement.TimeToLive">
      <summary>Gets or sets the time to live for the UDP binding element.</summary>
      <returns>The time to live for the UDP binding element.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement">
      <summary>Represents a UDP retransmission settings element.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement" /> class.</summary>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.DelayLowerBound">
      <summary>Gets or sets the lower bound delay for the UDP retransmission settings element.</summary>
      <returns>The lower bound delay for the UDP retransmission settings element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.DelayUpperBound">
      <summary>Gets or sets the upper bound delay for the UDP retransmission settings element.</summary>
      <returns>The upper bound delay for the UDP retransmission settings element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.MaxDelayPerRetransmission">
      <summary>Gets or sets the maximum delay per retransmission for the UDP retransmission settings element.</summary>
      <returns>The maximum delay for retransmission for the UDP retransmission settings element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.MaxMulticastRetransmitCount">
      <summary>Gets or sets the number of maximum multicast for retransmission for the UDP retransmission settings element.</summary>
      <returns>The number of maximum multicast for retransmission.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpRetransmissionSettingsElement.MaxUnicastRetransmitCount">
      <summary>Gets or sets the number of maximum unicast for retransmission for the UDP retransmission settings element.</summary>
      <returns>The number of maximum unicast for retransmission for the UDP retransmission settings element.</returns>
    </member>
    <member name="T:System.ServiceModel.Configuration.UdpTransportElement">
      <summary>Represents a configuration element that causes a channel to transfer messages on the UDP transport when it is included in a custom binding. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpTransportElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ServiceModel.Configuration.UdpTransportElement" /> class.</summary>
    </member>
    <member name="M:System.ServiceModel.Configuration.UdpTransportElement.ApplyConfiguration(System.ServiceModel.Channels.BindingElement)">
      <summary>Applies the settings of the specified <see cref="T:System.ServiceModel.Channels.BindingElement" /> to this configuration element.</summary>
      <param name="bindingElement">The <see cref="T:System.ServiceModel.Channels.BindingElement" /> whose settings are to be applied to this configuration element.</param>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.BindingElementType">
      <summary>Gets the <see cref="T:System.Type" /> object that represents the custom binding element.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the custom binding element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.DuplicateMessageHistoryLength">
      <summary>Gets or sets the value of the maximum number of message hashes used in a UDP transport configuration element.</summary>
      <returns>The value of the maximum number of message hashes used.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.MaxPendingMessagesTotalSize">
      <summary>Gets or sets the value of the maximum received message size property in a UDP transport element.</summary>
      <returns>The maximum size for a message that can be processed by the binding.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.MulticastInterfaceId">
      <summary>Gets or sets a value that uniquely identifies the network adapter that is used when sending and receiving multicast messages.</summary>
      <returns>The value of the network adapter that is used in a UDP binding element.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.RetransmissionSettings">
      <summary>Gets or sets the retransmission settings used in the <see cref="T:System.ServiceModel.Configuration.UdpTransportElement" />.</summary>
      <returns>The retransmission settings used.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.SocketReceiveBufferSize">
      <summary>Gets or sets the value of the receive buffer size in a UDP transport element.</summary>
      <returns>The receive buffer size on the underlying socket.</returns>
    </member>
    <member name="P:System.ServiceModel.Configuration.UdpTransportElement.TimeToLive">
      <summary>Gets or sets the time to live for the UDP binding element.</summary>
      <returns>The time to live for the UDP binding element.</returns>
    </member>
  </members>
</doc>