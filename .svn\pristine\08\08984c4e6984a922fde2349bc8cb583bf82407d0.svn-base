using System.Collections.Generic;
using System.Drawing;

namespace OCRTools
{
    public static class GraphicsExtension
    {
        public static void DrawSpline(this Graphics g, Pen pen, PointF[] points)
        {
            if (g == null || pen == null || points == null || points.Length == 0)
            {
                return;
            }
            List<Spline> list = new List<Spline>();
            foreach (PointF currentPoint in points)
            {
                if (list == null || list.Count == 0)
                {
                    Spline spline = new Spline();
                    spline.AddJoint(null, currentPoint);
                    list.Add(spline);
                }
                else
                {
                    Spline spline = new Spline();
                    Spline prevSpline = list[list.Count - 1];
                    spline.AddJoint(prevSpline, currentPoint);
                    list.Add(spline);
                }
            }
            for (int j = 0; j < list.Count; j++)
            {
                Spline spline2 = list[j];
                if (!spline2.IsFirst)
                {
                    spline2.Draw(g, pen);
                }
            }
        }
    }
}
