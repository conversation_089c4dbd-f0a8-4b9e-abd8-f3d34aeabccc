﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2020 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    [DefaultEvent("ColorChanged")]
    public class ColorPicker : UserControl
    {
        private ColorBox _colorBox;
        private ColorSlider _colorSlider;

        private DrawStyle _drawStyle;

        private MyColor _selectedColor;

        public ColorPicker()
        {
            InitializeComponent();
            DrawStyle = DrawStyle.Hue;
            _colorBox.ColorChanged += colorBox_ColorChanged;
            _colorSlider.ColorChanged += colorSlider_ColorChanged;
        }

        public MyColor SelectedColor
        {
            get => _selectedColor;
            private set
            {
                if (_selectedColor != value)
                {
                    _selectedColor = value;
                    _colorBox.SelectedColor = _selectedColor;
                    _colorSlider.SelectedColor = _selectedColor;
                }
            }
        }

        public DrawStyle DrawStyle
        {
            get => _drawStyle;
            set
            {
                if (_drawStyle != value)
                {
                    _drawStyle = value;
                    _colorBox.DrawStyle = value;
                    _colorSlider.DrawStyle = value;
                }
            }
        }

        public event ColorEventHandler ColorChanged;

        private void colorBox_ColorChanged(object sender, ColorEventArgs e)
        {
            _selectedColor = e.Color;
            _colorSlider.SelectedColor = SelectedColor;
            OnColorChanged();
        }

        private void colorSlider_ColorChanged(object sender, ColorEventArgs e)
        {
            _selectedColor = e.Color;
            _colorBox.SelectedColor = SelectedColor;
            OnColorChanged();
        }

        public void ChangeColor(Color color, ColorType colorType = ColorType.None)
        {
            SelectedColor = color;
            OnColorChanged(colorType);
        }

        private void OnColorChanged(ColorType colorType = ColorType.None)
        {
            if (ColorChanged != null) ColorChanged(this, new ColorEventArgs(SelectedColor, colorType));
        }

        #region Component Designer generated code

        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            _colorBox = new ColorBox();
            _colorSlider = new ColorSlider();
            SuspendLayout();
            //
            // colorBox
            //
            _colorBox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            _colorBox.DrawStyle = DrawStyle.Hue;
            _colorBox.Location = new System.Drawing.Point(0, 0);
            _colorBox.Name = "_colorBox";
            _colorBox.Size = new System.Drawing.Size(258, 258);
            _colorBox.TabIndex = 0;
            //
            // colorSlider
            //
            _colorSlider.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            _colorSlider.DrawStyle = DrawStyle.Hue;
            _colorSlider.Location = new System.Drawing.Point(257, 0);
            _colorSlider.Name = "_colorSlider";
            _colorSlider.Size = new System.Drawing.Size(32, 258);
            _colorSlider.TabIndex = 1;
            //
            // ColorPicker
            //
            AutoSize = true;
            Controls.Add(_colorBox);
            Controls.Add(_colorSlider);
            Name = "ColorPicker";
            Size = new System.Drawing.Size(292, 261);
            ResumeLayout(false);
        }

        #endregion Component Designer generated code
    }
}