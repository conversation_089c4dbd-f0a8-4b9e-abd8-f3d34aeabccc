﻿using Microsoft.Win32;
using OCRTools.Common;
using OCRTools.Language;
using ShareX.ScreenCaptureLib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    public class CommonMethod
    {
        private static readonly DefaultToastSetting ToastSetting = new DefaultToastSetting();

        private static ToolTip _txtTooTip;

        private static ToolStripDropDown _txtTipDropDown;

        private static FormTool FrmMsg;

        static CommonMethod()
        {
            InitRecentTask();
        }

        private static readonly string[] Suffix = { "Byte", "KB", "MB", "GB", "TB" };

        public static string FormatBytes(string fileName)
        {
            var size = new FileInfo(fileName).Length;
            return FormatBytes(size);
        }

        public static string FormatBytes(long bytes)
        {
            var i = 0;
            double dblSByte = bytes;
            if (bytes > 1024)
                for (i = 0; (bytes / 1024) > 0; i++, bytes /= 1024)
                    dblSByte = bytes / 1024.0;
            return string.Format("{0:0.##}{1}", dblSByte, Suffix[i]);
        }

        public static bool IsMatchUserLevel(string userLevel)
        {
            var result = true;
            if (!string.IsNullOrEmpty(userLevel))
            {
                result = userLevel.Contains("|" + Program.NowUser?.UserType + "|");
            }
            return result;
        }

        public static byte[] GetUrlBytes(string url, string cookie = null)
        {
            byte[] result = null;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    using (var client = new WebClient() { Proxy = null })
                    {
                        if (!string.IsNullOrEmpty(cookie))
                            client.Headers.Add(HttpRequestHeader.Cookie, cookie);
                        result = client.DownloadData(url);
                    }
                }
            }
            catch { }
            return result;
        }

        public const string StrKeFuQ = "365833440";
        public const string StrKeFuQun = "100029010";

        public static void OpenKeFuQ()
        {
            try
            {
                OpenUrl("http://wpa.qq.com/msgrd?v=3&uin=" + StrKeFuQ + "&site=qq&menu=yes");
                ClipboardService.SetText(StrKeFuQ);
            }
            catch { }
            ShowHelpMsg("正在打开客服QQ:" + StrKeFuQ + ".已复制到粘贴板,如打开失败,请手动添加好友！", 5000);
        }

        public static void OpenQun()
        {
            try
            {
                OpenUrl("https://qm.qq.com/cgi-bin/qm/qr?k=2DGDSxmtkAxbGL8Z9hVrx5YbXqgHfD8K&jump_from=webapi");
                ClipboardService.SetText(StrKeFuQun);
            }
            catch { }
            ShowHelpMsg("正在打开QQ群:" + StrKeFuQun + ".已复制到粘贴板,如打开失败,请手动加群！", 5000);
        }

        public static bool IsContainsChinese(string strTxt)
        {
            return Regex.IsMatch(strTxt, "[\u4e00-\u9fa5]");
        }

        //public static bool IsContainEnglish(string str)
        //{
        //    return Regex.IsMatch(str, @"[a-zA-Z]");
        //}

        public static bool IsAutoStart
        {
            get
            {
                try
                {
                    using (var currentUser = Registry.CurrentUser)
                    {
                        using (var registryKey =
                            currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                        {
                            return registryKey != null && registryKey.GetValue(CommonString.ProductName) != null;
                        }
                    }
                }
                catch (Exception)
                {
                }

                return false;
            }
        }

        private static void InitRecentTask()
        {
            if (Program.RecentTasks == null) Program.RecentTasks = new List<HistoryTask>();
            try
            {
                var files = Directory.GetFiles(string.IsNullOrEmpty(CommonSetting.截图文件保存路径)
                    ? CommonString.DefaultImagePath
                    : CommonSetting.截图文件保存路径);
                files = files.Reverse().Take((int)CommonSetting.最大历史记录数量).ToArray();
                foreach (var file in files)
                {
                    AddRecentTask(new HistoryTask
                    {
                        Status = TaskStatus.History,
                        Info = new HistoryItem
                        {
                            DataType = EDataType.Image,
                            FilePath = file,
                            CreateTime = File.GetCreationTime(file)
                        }
                    });
                }
                Program.RecentTasks.Reverse();
            }
            catch
            {
            }
        }

        public static void ClearAllTask()
        {
            Program.RecentTasks.Clear();
        }

        public static void AddRecentTask(HistoryTask task)
        {
            if (task != null)
            {
                if (Program.RecentTasks.Count >= CommonSetting.最大历史记录数量) Program.RecentTasks.RemoveAt(0);
                if (!string.IsNullOrEmpty(task.Info.FilePath))
                    task.Info.FilePath = task.Info.FilePath.Replace("\\\\", "\\");
                Program.RecentTasks.Add(task);
            }
        }

        public static void RemoveRecentTask(HistoryTask task)
        {
            if (task != null) Program.RecentTasks.Remove(task);
        }

        public static DateTime GetAssemblyDate(string fileName)
        {
            var result = DateTime.MinValue;
            try
            {
                if (File.Exists(fileName))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(fileName);
                    if (!string.IsNullOrEmpty(versionInfo.Comments) && versionInfo.Comments.Contains("("))
                        result = DateTime.Parse(SubString(versionInfo.Comments, "(").Replace("(", "").Replace(")", ""));
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        public static DateTime GetLastWriteDate(string fileName)
        {
            var result = DateTime.MinValue;
            try
            {
                if (File.Exists(fileName))
                {
                    result = new FileInfo(fileName).LastWriteTime;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return result;
        }

        //ShowNotificationTip(string.Format(Resources.TaskHelpers_OpenQuickScreenColorPicker_Copied_to_clipboard___0_, text),
        //                "OCRTools - " + Resources.ScreenColorPicker);
        public static void ShowNotificationTip(string text, string title = null, int duration = -1
            , string font = null, float fontSize = 15, string foreColor = null, string backColor = null, string url = null, string data = null)
        {
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                ShowNotificationTip(text, title, null, duration, font, fontSize, foreColor, backColor, url, data);
            });
        }

        public static void ShowCaptureNotificationTip(string fileName, int duration = -1)
        {
            CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
            {
                ShowNotificationTip(null, "截图".CurrentText() + "-" + CommonString.FullName.CurrentText(), fileName, duration);
            });
        }

        public static void ShowNotificationTip(string text, string title, string fileName = null, int duration = -1
            , string font = null, float fontSize = 15, string foreColor = null, string backColor = null, string url = null, string data = null)
        {
            if (string.IsNullOrEmpty(title)) title = CommonString.FullName.CurrentText();
            if (duration <= 0) duration = (int)(ToastSetting.ToastWindowDuration * 1000);

            var toastConfig = new NotificationFormConfig
            {
                Duration = duration,
                FadeDuration = (int)(ToastSetting.ToastWindowFadeDuration * 1000),
                Placement = ToastSetting.ToastWindowPlacement,
                Size = ToastSetting.ToastWindowSize,
                Title = title,
                Text = text,
                FilePath = fileName,
                Url = url,
                Data = data
            };
            if (!string.IsNullOrEmpty(font))
            {
                toastConfig.TextFont = CommonString.GetFont(font, fontSize, FontStyle.Regular, CommonString.CommonGraphicsUnit(), false);
            }
            if (!string.IsNullOrEmpty(foreColor))
            {
                toastConfig.TextColor = Color.FromName(foreColor);
            }
            if (!string.IsNullOrEmpty(backColor))
            {
                toastConfig.BackgroundColor = Color.FromName(backColor);
            }
            if (toastConfig.LeftClickAction == ToastClickAction.None)
            {
                if (!string.IsNullOrEmpty(toastConfig.Url))
                    toastConfig.LeftClickAction = ToastClickAction.OpenUrl;
                else if (!string.IsNullOrEmpty(toastConfig.FilePath) || toastConfig.Image != null)
                    toastConfig.LeftClickAction = ToastClickAction.ViewImage;
                else if (!string.IsNullOrEmpty(toastConfig.Data))
                    toastConfig.LeftClickAction = ToastClickAction.OpenForm;
                //toastConfig.LeftClickAction = ToastClickAction.OpenFolder;
            }
            NotificationForm.Show(toastConfig);
        }

        public static void AutoStart(bool isAdd)
        {
            try
            {
                //if (Equals(isAdd, CommonSetting.开机启动)) return;
                using (var currentUser = Registry.CurrentUser)
                {
                    using (var registryKey =
                        currentUser.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"))
                    {
                        if (registryKey != null)
                        {
                            if (isAdd)
                            {
                                if (registryKey.GetValue(CommonString.ProductName) == null)
                                    registryKey.SetValue(CommonString.ProductName,
                                        Application.ExecutablePath.Replace("/", "\\"));
                            }
                            else
                            {
                                if (registryKey.GetValue(CommonString.ProductName) != null)
                                    registryKey.DeleteValue(CommonString.ProductName, false);
                            }

                            registryKey.Close();
                        }
                    }

                    currentUser.Close();
                }
            }
            catch (Exception)
            {
                ShowHelpMsg("设置开机启动失败，如果被拦截，请点击允许！或者右键->以管理员方式打开重试！".CurrentText());
            }
        }

        public static bool OpenFolder(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && Directory.Exists(filePath))
                try
                {
                    using (var process = new Process())
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };

                        process.StartInfo = psi;
                        process.Start();
                    }

                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static bool OpenFile(string filePath, bool isShowWindow = true)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                using (var process = new Process())
                {
                    try
                    {
                        var psi = new ProcessStartInfo
                        {
                            FileName = filePath
                        };
                        if (!isShowWindow)
                        {
                            psi.CreateNoWindow = true;
                            psi.UseShellExecute = false;
                        }

                        process.StartInfo = psi;
                        process.Start();
                    }
                    catch { }
                    return true;
                }
            }

            return false;
        }

        public static bool OpenFolderWithFile(string filePath)
        {
            filePath = filePath.Replace("\\\\", "\\");
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                try
                {
                    NativeMethods.OpenFolderAndSelectFile(filePath);
                    return true;
                }
                catch
                {
                }

            return false;
        }

        public static void DetermineCall(Control ctrl, MethodInvoker method)
        {
            try
            {
                if (ctrl.InvokeRequired)
                    ctrl.Invoke(method);
                else
                    method();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static void OpenUrl(string url)
        {
            if (!string.IsNullOrEmpty(url))
                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        using (var process = new Process())
                        {
                            var psi = new ProcessStartInfo();

                            psi.FileName = "cmd.exe";
                            psi.UseShellExecute = false;    //不使用shell启动
                            psi.RedirectStandardInput = true;//喊cmd接受标准输入
                            psi.RedirectStandardOutput = false;//不想听cmd讲话所以不要他输出
                            psi.RedirectStandardError = true;//重定向标准错误输出
                            psi.CreateNoWindow = true;//不显示窗口
                            process.StartInfo = psi;
                            process.Start();

                            //向cmd窗口发送输入信息 后面的&exit告诉cmd运行好之后就退出
                            process.StandardInput.WriteLine("start " + url.Replace("&", "^&") + " &exit");
                            process.StandardInput.AutoFlush = true;
                            process.WaitForExit();//等待程序执行完退出进程
                            process.Close();
                        }
                    }
                    catch
                    {
                    }
                });
        }

        public static void OpenForm(string cName)
        {
            if (string.IsNullOrEmpty(cName)) return;
            try
            {
                (Assembly.GetExecutingAssembly().CreateInstance(cName) as Form)?.ShowDialog();
            }
            catch
            {
            }
        }

        /// <summary>
        ///     引用user32.dll动态链接库（windows api），
        ///     使用库中定义 API：SetCursorPos
        /// </summary>
        [DllImport("user32.dll")]
        public static extern int SetCursorPos(int x, int y);

        //private static PopupForm popupForm;
        public static void ShowTxtToolTip(UcContent parentCtrl, TransParentLabel tipControl)
        {
            if (_txtTooTip == null)
            {
                _txtTooTip = new ToolTip { ToolTipIcon = ToolTipIcon.Info, OwnerDraw = true };
                var tipStringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center,
                    //FormatFlags = StringFormatFlags.NoWrap,
                    HotkeyPrefix = HotkeyPrefix.None
                };
                _txtTooTip.Popup += (sender, e) =>
                 {
                     e.ToolTipSize = new Size((int)(e.ToolTipSize.Width * 1.25)
                         , (int)(e.ToolTipSize.Height * 1.25));
                 };
                _txtTooTip.Draw += (sender, e) =>
                {
                    e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
                    e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
                    e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                    e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;

                    using (var contentBrush = new SolidBrush(CommonSetting.Get默认背景颜色()))
                    {
                        e.Graphics.FillRectangle(contentBrush, e.Bounds);
                    }

                    e.Graphics.DrawString(e.ToolTipText, CommonString.GetSysBoldFont(14), new SolidBrush(Color.FromArgb(255, CommonSetting.Get默认文字颜色())), //SystemBrushes.ControlText,
                        new RectangleF(e.Bounds.X, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height - 4),
                        tipStringFormat);

                    e.DrawBorder();
                    //using (var penBorder = new Pen(Color.Gray, 1))
                    //{
                    //    Rectangle rectBorder = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width - 1, e.Bounds.Height - 1);
                    //    e.Graphics.DrawRectangle(penBorder, e.Bounds);
                    //}
                };
            }

            tipControl.MouseEnter += (sender, e) =>
            {
                tipControl.BringToFront();
                tipControl.IsMouseMove = true;
                if (!string.IsNullOrEmpty(tipControl.Text))
                {
                    var mouseToForm = parentCtrl.PointToClient(Control.MousePosition);
                    var ctrlToForm = tipControl.PointToClient(Control.MousePosition);
                    var x = mouseToForm.X - ctrlToForm.X;
                    x = x < 0 ? 0 : x;
                    var y = mouseToForm.Y;
                    var verHeight = tipControl.Height - ctrlToForm.Y;
                    if (verHeight + y > parentCtrl.Height)
                        y += 20;
                    else
                        y += verHeight;

                    _txtTooTip.Show(tipControl.Text, parentCtrl, new Point(x, y));
                }
            };
            tipControl.MouseLeave += (sender, e) =>
            {
                tipControl.IsMouseMove = false;
                if (!string.IsNullOrEmpty(tipControl.Text)) _txtTooTip.Hide(parentCtrl);
            };
        }

        //static List<float> ScaleStep = new List<float>() { 10, 1, 0.1f };
        public static Font ScaleLabel(string strText, Font baseFont, Size baseSize)
        {
            //Font font = baseFont;
            //Stopwatch stop = Stopwatch.StartNew();
            var font = string.IsNullOrWhiteSpace(strText) ? baseFont : ScaleLabelByStep(strText, baseFont, baseSize, 0.1f);
            //ScaleStep.ForEach(p =>
            //{
            //    font = ScaleLabelByStep(strText, baseFont, baseSize, p);
            //});
            //Console.WriteLine(strText + ":" + stop.ElapsedMilliseconds.ToString("F0") + "ms");
            return font;
        }

        //static List<float> ScaleStep = new List<float>() { 10, 1, 0.1f };
        public static Font ScaleLabelByHeight(string strText, Font baseFont, Size baseSize, float maxSize = 0)
        {
            //Font font = baseFont;
            //Stopwatch stop = Stopwatch.StartNew();
            var font = string.IsNullOrWhiteSpace(strText) ? baseFont : ScaleLabelBySize(strText, baseFont, baseSize, 0.1f, maxSize);
            //ScaleStep.ForEach(p =>
            //{
            //    font = ScaleLabelByStep(strText, baseFont, baseSize, p);
            //});
            //Console.WriteLine(strText + ":" + stop.ElapsedMilliseconds.ToString("F0") + "ms");
            return font;
        }

        static Font ScaleLabelByStep(string strText, Font baseFont, Size baseSize, float stepSize)
        {
            //decrease font size if text is wider or higher than
            while (baseFont.Size - stepSize > 0)
            {
                var font = CommonString.GetFont(baseFont.FontFamily.Name, baseFont.Size - stepSize, baseFont.Style, baseFont.Unit, false);
                var nextSize = GetFontSize(strText, font, baseSize);

                //dont make text width or hight bigger than
                if (nextSize.Width < baseSize.Width || nextSize.Height < baseSize.Height)
                    break;

                baseFont = font;
            }

            //increase font size if width is bigger than text size
            while (true)
            {
                var font = CommonString.GetFont(baseFont.FontFamily.Name, baseFont.Size + stepSize, baseFont.Style, baseFont.Unit, false);
                var nextSize = GetFontSize(strText, font, baseSize);

                //dont make text width or hight bigger than
                if (nextSize.Width > baseSize.Width || nextSize.Height > baseSize.Height)
                    break;

                baseFont = font;
            }

            return baseFont;
        }

        static Font ScaleLabelBySize(string strText, Font baseFont, Size baseSize, float stepSize, float maxSize = 0)
        {
            //decrease font size if text is wider or higher than
            while (baseFont.Size - stepSize > 0)
            {
                var font = CommonString.GetFont(baseFont.FontFamily.Name, baseFont.Size - stepSize, baseFont.Style, baseFont.Unit, false);
                var nextSize = GetFontSize(strText, font, baseSize);

                //dont make text width or hight bigger than
                if (nextSize.Height < baseSize.Height)
                    break;

                baseFont = font;
            }

            //increase font size if width is bigger than text size
            while (true)
            {
                var font = CommonString.GetFont(baseFont.FontFamily.Name, baseFont.Size + stepSize, baseFont.Style, baseFont.Unit, false);
                var nextSize = GetFontSize(strText, font, baseSize);

                //dont make text width or hight bigger than
                if (nextSize.Height > baseSize.Height)
                    break;

                baseFont = font;
            }

            if (maxSize > 0 && baseFont.Size > maxSize)
            {
                baseFont = CommonString.GetFont(baseFont.FontFamily.Name, maxSize, baseFont.Style, baseFont.Unit, false);
            }

            return baseFont;
        }

        private static Size GetFontSize(string text, Font font, Size size)
        {
            return TextRenderer.MeasureText(text, font, size, TextFormatFlags.WordBreak);
        }

        public static void ShowTxtToolTipContextMenu(TransParentLabel ctrl, Point location)
        {
            TextBox txtBox;
            if (_txtTipDropDown == null)
            {
                _txtTipDropDown = new ToolStripDropDown
                {
                    Padding = Padding.Empty,
                    Margin = Padding.Empty,
                    TabStop = false
                };
                txtBox = new TextBox
                {
                    BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle,
                    AutoSize = false,
                    Multiline = true,
                    Margin = CommonString.PaddingZero,
                    Font = CommonSetting.默认文字字体,
                    //Location = ctrl.Location,
                    TabStop = false
                };
                ToolStripItem lblInfo = new ToolStripControlHost(txtBox)
                {
                    //DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                    AutoSize = false,
                    Padding = CommonString.PaddingZero,
                    Margin = CommonString.PaddingZero,
                    DisplayStyle = ToolStripItemDisplayStyle.Text
                    //Image = Properties.Resources.复制,
                };
                _txtTipDropDown.Items.Add(lblInfo);
                _txtTipDropDown.VisibleChanged += (sender, e) =>
                {
                    var sourceControl = sender?.GetType()
                        .GetProperty("SourceControlInternal", BindingFlags.Instance | BindingFlags.NonPublic)
                        ?.GetValue(sender, null);
                    if (sourceControl != null && sourceControl is TransParentLabel label)
                        label.IsClickToView = _txtTipDropDown.Visible;
                };
            }
            else
            {
                txtBox = (_txtTipDropDown.Items[0] as ToolStripControlHost).Control as TextBox;
            }

            if (txtBox != null)
            {
                txtBox.Text = ctrl.Text;
                var preferredSize = txtBox.PreferredSize;
                if (preferredSize.Width < 100) preferredSize.Width = (int)(preferredSize.Width * 1.5d);
                //preferredSize.Width = Math.Max(preferredSize.Width, ctrl.Width);
                txtBox.Size = preferredSize;
            }

            _txtTipDropDown.Show(ctrl, location);

            if (txtBox != null)
            {
                txtBox.Focus();
                txtBox.SelectAll();
            }
        }

        public static void SetStyle(Control control, ControlStyles styles,
            bool newValue)
        {
            typeof(Control).GetMethod("SetStyle",
                BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.InvokeMethod)?.Invoke(control, new object[] { styles, newValue });
        }

        //双缓冲
        public static void EnableDoubleBuffering(Control ctrl)
        {
            try
            {
                // Set the value of the double-buffering style bits to true.
                var info = ctrl.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic);
                if (info is null)
                {
                    return;
                }
                info.SetValue(ctrl, true, null);

                foreach (Control subCtrl in ctrl.Controls) EnableDoubleBuffering(subCtrl);
            }
            catch
            {
            }
        }

        public static string GetFileExt(string fileName, string fileExt = null)
        {
            try
            {
                if (string.IsNullOrEmpty(fileExt))
                {
                    if (fileName.StartsWith("data:txt"))
                        fileExt = CommonString.StrDefaultTxtType;
                    else if (fileName.StartsWith("data:") || fileName.StartsWith("http"))
                        fileExt = CommonString.StrDefaultImgType;
                }

                if (string.IsNullOrEmpty(fileExt))
                {
                    fileExt = Path.GetExtension(fileName).TrimStart('.').ToLower();
                    if (!string.IsNullOrEmpty(fileExt) && fileExt.Contains("?"))
                        fileExt = CommonString.SubString(fileExt, "", "?");
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }

            return fileExt;
        }

        public static void ShowHelpMsg(string msg, int totalMilsec = 5000, string strFrom = null)
        {
            if (FrmMsg == null || FrmMsg.IsDisposed)
            {
                CommonMethod.DetermineCall(FrmMain.FrmTool, () =>
                {
                    FrmMsg = new FormTool();
                    FrmMsg.SetWindowLong();
                });
            }
            Task.Factory.StartNew(() =>
            {
                try
                {
                    if (string.IsNullOrEmpty(msg))
                    {
                        msg = string.Format("欢迎使用{0}！", CommonString.FullName);
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(strFrom)) msg = string.Format("{0} {1}", CommonString.FullName.CurrentText(), msg);
                    }

                    if (FrmMsg == null) return;
                    FrmMsg.DelayMilSec = totalMilsec;
                    FrmMsg.NowID = Guid.NewGuid().ToString();
                    FrmMsg.DrawStr(msg, FrmMsg.NowID);
                    FrmMsg.TopMost = true;
                    FrmMsg.BringToFront();
                }
                catch
                {
                }
            });
        }

        #region 初始化数据

        public static void Exit()
        {
            try
            {
                CommonString.IsExit = true;
                Application.ExitThread();
                Application.Exit();
                Process.GetCurrentProcess().Kill();
            }
            catch (Exception oe)
            {
                Log.WriteError("退出异常", oe);
            }

            try
            {
                Environment.Exit(0);
            }
            catch
            {
            }
        }

        #endregion

        #region 站点可用性相关

        public static string GetSubStrByUrl(string strUrl)
        {
            var strTmp = "";
            if (!string.IsNullOrEmpty(strUrl))
                try
                {
                    if (strUrl.Contains("&"))
                    {
                        strTmp = SubString(strUrl, "", "&");
                        var strEncrpt = SubString(strUrl, "&");
                        strEncrpt = CommonEncryptHelper.DesEncrypt(strEncrpt, CommonString.StrCommonEncryptKey);
                        //strURL = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                        strTmp += "&con=" + HttpUtility.UrlEncode(strEncrpt);
                    }
                    else
                    {
                        strTmp = strUrl;
                    }
                }
                catch (Exception)
                {
                    strTmp = strUrl;
                    //Log.WriteError("GetSubStrByURL出错", oe);
                }

            return strTmp;
        }

        public static int ExecTimeOutSecond = 35;

        public static string Identity;

        private static string UserDomainName;

        public static string GetMachineId()
        {
            var result = string.Empty;
            try
            {
                var mos = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct");
                foreach (ManagementBaseObject mo in mos.Get())
                {
                    result = mo["UUID"] as string;
                }
            }
            catch
            {
            }

            return result;
        }

        private static string GetMachineGuid()
        {
            var result = string.Empty;
            try
            {
                var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, Environment.Is64BitOperatingSystem ? RegistryView.Registry64 : RegistryView.Registry32);
                using (var subKey = baseKey.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography", RegistryKeyPermissionCheck.ReadSubTree))
                {
                    if (subKey != null)
                    {
                        var value = subKey.GetValue("MachineGuid", "default");
                        if (value != null && value.ToString() != "default")
                        {
                            result = value.ToString();
                        }
                        subKey.Close();
                        subKey.Dispose();
                    }
                    baseKey.Close();
                    baseKey.Dispose();
                }
            }
            catch (Exception)
            {
            }

            return result;
        }

        public static NameValueCollection GetRequestHeader()
        {
            if (string.IsNullOrEmpty(UserDomainName))
            {
                try
                {
                    UserDomainName = Environment.UserDomainName + "-" + Environment.UserName;
                }
                catch
                {
                    try
                    {
                        UserDomainName = Environment.MachineName + "-" + Environment.UserName;
                    }
                    catch { }
                }
            }
            if (string.IsNullOrEmpty(Identity))
            {
                Identity = GetMachineGuid() + ":" + GetMachineId();
                if (string.IsNullOrEmpty(Identity))
                    try
                    {
                        var info = new FileInfo(Application.ExecutablePath);
                        Identity = new List<DateTime> { info.CreationTime, info.LastWriteTime }.Min().ToString("yyyy-MM-dd HH:mm:ss fff");
                    }
                    catch
                    {
                        try
                        {
                            Identity = File.GetCreationTime(Application.StartupPath).ToString("yyyy-MM-dd HH:mm:ss fff");
                        }
                        catch { }
                    }
                try
                {
                    Identity = ToMd5(UserDomainName + Identity);
                }
                catch
                {
                    try
                    {
                        Identity = GetMd5Hash(UserDomainName + Identity);
                    }
                    catch { }
                }
            }
            var header = new NameValueCollection
            {
                {"mac", HttpUtility.UrlEncode(UserDomainName)},
                {"uid",Identity},
                {"ver", CommonString.DtNowDate.ToString("yyyy-MM-dd HH:mm:ss")},
                {"tick",ServerTime.DateTime.Ticks.ToString()}
            };
            if (Program.IsLogined())
            {
                header.Add("app", Program.NowUser?.Account);
                header.Add("reg", Program.NowUser?.DtReg.ToString("yyyy-MM-dd HH:mm:ss"));
                header.Add("token", Program.NowUser?.Token);
            }
            return header;
        }

        static string ToMd5(string s, int len = 32)
        {
            using (var md5Hasher = new MD5CryptoServiceProvider())
            {
                var data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
                var sb = new StringBuilder();
                foreach (var t in data)
                {
                    sb.Append(t.ToString("x2"));
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }

        static string GetMd5Hash(string input, int len = 32)
        {
            using (MD5 md5 = MD5.Create())
            {
                var data = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                var sb = new StringBuilder();
                foreach (byte t in data)
                {
                    sb.Append(t.ToString("x2")); // 将每个字节转换为16进制字符串
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }

        public static string GetServerHtml(string url, string strNowSite
            , bool isDecodeUrl = true, bool isPost = false, string strPost = "")
        {
            var html = "";
            try
            {
                var headers = GetRequestHeader();
                if (isDecodeUrl)
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", strPost, "", ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByUrl(url), "", "", "", "", ExecTimeOutSecond, headers);
                }
                else
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", strPost, "", ExecTimeOutSecond, headers);
                    else
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", "", "", ExecTimeOutSecond, headers);
                }
            }
            catch (Exception)
            {
                //Log.WriteError("GetServerHtml出错", oe);
            }

            return html;
        }

        #endregion

        #region 共享方法

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static void ExecCmdWithNoResult(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                p.StartInfo.Verb = "runas";
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
            }
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        #endregion

        public static DateTime ConvertToDateTime(string timestamp)
        {
            DateTime time = DateTime.MinValue;
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            if (timestamp.Length == 10)//精确到秒
            {
                time = startTime.AddSeconds(double.Parse(timestamp));
            }
            else if (timestamp.Length == 13)//精确到毫秒
            {
                time = startTime.AddMilliseconds(double.Parse(timestamp));
            }
            return time;
        }

        public static string NumberToLetters(int num)
        {
            string result = "";
            while (--num >= 0)
            {
                result = (char)('A' + (num % 26)) + result;
                num /= 26;
            }
            return result;
        }

        private static string GetNextRomanNumeralStep(ref int num, int step, string numeral)
        {
            string result = "";
            if (num >= step)
            {
                result = numeral.Repeat(num / step);
                num %= step;
            }
            return result;
        }

        public static string NumberToRomanNumeral(int num)
        {
            string result = "";
            result += GetNextRomanNumeralStep(ref num, 1000, "M");
            result += GetNextRomanNumeralStep(ref num, 900, "CM");
            result += GetNextRomanNumeralStep(ref num, 500, "D");
            result += GetNextRomanNumeralStep(ref num, 400, "CD");
            result += GetNextRomanNumeralStep(ref num, 100, "C");
            result += GetNextRomanNumeralStep(ref num, 90, "XC");
            result += GetNextRomanNumeralStep(ref num, 50, "L");
            result += GetNextRomanNumeralStep(ref num, 40, "XL");
            result += GetNextRomanNumeralStep(ref num, 10, "X");
            result += GetNextRomanNumeralStep(ref num, 9, "IX");
            result += GetNextRomanNumeralStep(ref num, 5, "V");
            result += GetNextRomanNumeralStep(ref num, 4, "IV");
            result += GetNextRomanNumeralStep(ref num, 1, "I");
            return result;
        }

        public static Size MeasureText(string text, Font font)
        {
            using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
            {
                return g.MeasureString(text, font).ToSize();
            }
        }

        public static Icon GetApplicationIcon()
        {
            return FrmMain.FrmTool?.Icon;
        }

        public static void LoadHtml(Control ctrl, Point location, Size size, string url, Dictionary<string, string> dicCheck = null)
        {
            ctrl.LoadHtml(location, size, url, dicCheck);
        }

        public static bool IsHasPermission(string DirectoryPath)
        {
            if (string.IsNullOrEmpty(DirectoryPath))
                return false;
            try
            {
                var rules = Directory.GetAccessControl(DirectoryPath).GetAccessRules(true, true, typeof(SecurityIdentifier));
                var identity = WindowsIdentity.GetCurrent();
                foreach (FileSystemAccessRule rule in rules)
                {
                    if (identity.Groups.Contains(rule.IdentityReference))
                    {
                        if ((FileSystemRights.Modify & rule.FileSystemRights) == FileSystemRights.Modify)
                        {
                            if (rule.AccessControlType == AccessControlType.Allow)
                                return true;
                        }
                    }
                }
            }
            catch { }
            return false;
        }

        /// <summary>
        /// 复制文件夹及文件
        /// </summary>
        /// <param name="sourceFolder">原文件路径</param>
        /// <param name="destFolder">目标文件路径</param>
        /// <returns></returns>
        static int CopyFolder(string sourceFolder, string destFolder)
        {
            try
            {
                //如果目标路径不存在,则创建目标路径
                if (!Directory.Exists(destFolder))
                {
                    Directory.CreateDirectory(destFolder);
                }
                //得到原文件根目录下的所有文件
                string[] files = Directory.GetFiles(sourceFolder);
                foreach (string file in files)
                {
                    string name = Path.GetFileName(file);
                    string dest = Path.Combine(destFolder, name);
                    File.Copy(file, dest);//复制文件
                }
                //得到原文件根目录下的所有文件夹
                string[] folders = Directory.GetDirectories(sourceFolder);
                foreach (string folder in folders)
                {
                    string name = Path.GetFileName(folder);
                    string dest = Path.Combine(destFolder, name);
                    CopyFolder(folder, dest);//构建目标路径,递归复制文件
                }
                return 1;
            }
            catch (Exception e)
            {
                return -1;
            }
        }

        public static RegionCaptureOptions RegionCaptureOptions;

        public static RegionCaptureOptions GetRegionCaptureOptions(bool isQuick, string customTitle)
        {
            if (RegionCaptureOptions == null)
            {
                RegionCaptureOptions = new RegionCaptureOptions
                {
                    AnnotationOptions = new AnnotationOptions()
                };
                //load config
                InitLastSetting(RegionCaptureOptions.AnnotationOptions);
            }
            RegionCaptureOptions.QuickCrop = isQuick;
            RegionCaptureOptions.CustomInfoTitle = customTitle;
            return RegionCaptureOptions;
        }

        public class CaptureImageContent
        {
            public Bitmap Image { get; set; }

            public Rectangle Rectangle { get; set; }

            public Color Color { get; set; }
            public WindowInfo Window { get; set; }
            public ConcurrentBag<WindowInfo> Windows { get; internal set; }
        }

        public static CaptureImageContent EditImage(Image image)
        {
            var img = new CaptureImageContent() { Image = image == null ? null : new Bitmap(image) };
            return EditImage(img);
        }

        public static CaptureImageContent EditImage(CaptureImageContent img)
        {
            var result = CatchImage("图片编辑", RegionCaptureMode.Editor, img);
            if (result?.Image != null)
            {
                SaveImage(result.Image, false);
            }
            return result;
        }

        public static bool IsImageEditModel(string operate)
        {
            return Equals(operate, CaptureActions.截图贴图.ToString()) || Equals(operate, CaptureActions.截图编辑.ToString());
        }

        public static CaptureImageContent CatchImage(string operate, RegionCaptureMode mode = RegionCaptureMode.Annotation
            , CaptureImageContent img = null)
        {
            CaptureImageContent result = null;
            if (mode != RegionCaptureMode.Editor)
                StaticValue.IsCatchScreen = true;
            using (var form = new RegionCaptureForm(
                mode, GetRegionCaptureOptions(true, operate)
                , img == null || img.Image == null ? null : new Bitmap(img.Image)
                )
            {
                Icon = FrmMain.FrmTool.Icon
            })
            {
                try
                {
                    if (img != null && img.Windows != null && Equals(operate, "图片编辑"))
                    {
                        form.ShapeManager.Windows = img.Windows;
                        form.ShapeManager.LastAllScreenRectangle = img.Rectangle;
                    }
                    form.ShowDialog();
                    form.IsExitCapture = true;
                    if (form.Result != RegionResult.Close)
                    {
                        result = new CaptureImageContent()
                        {
                            Image = form.GetResultImage(),
                            Color = form.ShapeManager?.GetCurrentColor() ?? Color.Empty,
                            Rectangle = form.RectangleResult,
                            Window = form.SelectedWindow
                        };
                        StaticValue.LastRectangle = result.Rectangle;
                        if (IsImageEditModel(operate))
                        {
                            result.Windows = form.ShapeManager.Windows;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    if (mode != RegionCaptureMode.Editor)
                    {
                        StaticValue.IsCatchScreen = false;
                        try
                        {
                            SaveLastSetting(RegionCaptureOptions.AnnotationOptions);
                        }
                        catch (Exception oe)
                        {
                        }
                    }
                }
            }
            return result;
        }

        public static string SaveImage(Image image, bool isCapture)
        {
            if (CommonSetting.复制到粘贴板)
                ClipboardService.ClipSetImage(image, false); ;
            if (CommonSetting.自动保存)
            {
                var fileName = image.SaveFileWithOutConfirm(null, !isCapture);
                if (isCapture && CommonSetting.显示Toast通知)
                    Task.Factory.StartNew(() =>
                    {
                        System.Threading.Thread.CurrentThread.Priority = System.Threading.ThreadPriority.Highest;
                        ShowCaptureNotificationTip(fileName);
                    }, System.Threading.CancellationToken.None, TaskCreationOptions.LongRunning, TaskScheduler.Default);
                return fileName;
            }

            return string.Empty;
        }

        static void InitLastSetting(object objTarget, string parent = "")
        {
            var type = objTarget.GetType();
            var properties = type.GetProperties();
            if (properties.Length <= 0) return;

            foreach (var item in properties)
            {
                if (!item.CanWrite)
                {
                    continue;
                }
                var objKey = (string.IsNullOrEmpty(parent) ? "" : (parent + "-")) + item.Name;
                if (!Equals(item.PropertyType, typeof(string)) && item.PropertyType.IsClass)
                {
                    var objValue = type?.GetProperty(item.Name).GetValue(objTarget, null);
                    InitLastSetting(objValue, objKey);
                    continue;
                }
                string strValue = CommonSetting.GetValue<string>(type.Name, objKey);
                if (strValue == null) continue;
                try
                {
                    var value = TypeDescriptor.GetConverter(item.PropertyType).ConvertFromString(strValue);
                    type?.GetProperty(item.Name).SetValue(objTarget, value, null);
                }
                catch (Exception oe)
                {
                }
            }
        }

        static void SaveLastSetting(object objTarget, string parent = "")
        {
            var type = objTarget.GetType();
            var properties = type.GetProperties();
            if (properties.Length <= 0) return;
            foreach (var item in properties)
            {
                if (!item.CanWrite)
                {
                    continue;
                }
                var objKey = (string.IsNullOrEmpty(parent) ? "" : (parent + "-")) + item.Name;
                object value = type?.GetProperty(item.Name).GetValue(objTarget, null);
                if (!Equals(item.PropertyType, typeof(string)) && item.PropertyType.IsClass)
                {
                    SaveLastSetting(value, objKey);
                    continue;
                }
                var valueStr = TypeDescriptor.GetConverter(item.PropertyType).ConvertToInvariantString(value);
                CommonSetting.SetValue(type.Name, objKey, valueStr);
            }
        }
    }
}