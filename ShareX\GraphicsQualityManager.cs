﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace OCRTools
{
    public class GraphicsQualityManager : IDisposable
    {
        private CompositingQuality previousCompositingQuality;
        private InterpolationMode previousInterpolationMode;
        private SmoothingMode previousSmoothingMode;
        private Graphics g;

        public GraphicsQualityManager(Graphics g, bool setHighQuality = true)
        {
            this.g = g;

            previousCompositingQuality = g.CompositingQuality;
            previousInterpolationMode = g.InterpolationMode;
            previousSmoothingMode = g.SmoothingMode;

            if (setHighQuality)
            {
                SetHighQuality();
            }
        }

        public void SetHighQuality()
        {
            if (g != null)
            {
                g.CompositingQuality = CompositingQuality.HighQuality;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = SmoothingMode.HighQuality;
            }
        }

        public void Dispose()
        {
            if (g != null)
            {
                g.CompositingQuality = previousCompositingQuality;
                g.InterpolationMode = previousInterpolationMode;
                g.SmoothingMode = previousSmoothingMode;
            }
        }
    }
}
