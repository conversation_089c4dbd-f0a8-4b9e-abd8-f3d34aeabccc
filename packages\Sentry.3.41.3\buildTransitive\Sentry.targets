<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <SentryAttributesFile>Sentry.Attributes$(MSBuildProjectExtension.Replace('proj', ''))</SentryAttributesFile>
  </PropertyGroup>

  <Target Name="WriteSentryAttributes"
          Condition="$(Language) == 'VB' or $(Language) == 'C#' or $(Language) == 'F#'"
          BeforeTargets="BeforeCompile;CoreCompile"
          Inputs="$(MSBuildAllProjects)"
          Outputs="$(IntermediateOutputPath)$(SentryAttributesFile)">
    <PropertyGroup>
      <SentryAttributesFilePath>$(IntermediateOutputPath)$(SentryAttributesFile)</SentryAttributesFilePath>
    </PropertyGroup>
    <ItemGroup>
      <SentryAttributes Include="System.Reflection.AssemblyMetadata">
        <_Parameter1>Sentry.ProjectDirectory</_Parameter1>
        <_Parameter2>$(ProjectDir)</_Parameter2>
      </SentryAttributes>
      <!-- Ensure not part of Compile, as a workaround for https://github.com/dotnet/sdk/issues/114 -->
      <Compile Remove="$(SentryAttributesFilePath)" />
    </ItemGroup>
    <WriteCodeFragment AssemblyAttributes="@(SentryAttributes)" Language="$(Language)" OutputFile="$(SentryAttributesFilePath)">
      <Output Condition="$(Language) != 'F#'" TaskParameter="OutputFile" ItemName="Compile" />
      <Output Condition="$(Language) == 'F#'" TaskParameter="OutputFile" ItemName="CompileBefore" />
      <Output TaskParameter="OutputFile" ItemName="FileWrites" />
    </WriteCodeFragment>
  </Target>

  <Target Name="CheckSentryCLI" AfterTargets="Build" Condition="'$(InnerTargets)' == ''">
    <PropertyGroup>
      <!-- Set defaults for the Sentry properties. -->
      <SentryUploadSymbols Condition="'$(SentryUploadSymbols)' == ''">false</SentryUploadSymbols>
      <SentryUploadSources Condition="'$(SentryUploadSources)' == ''">false</SentryUploadSources>

      <!-- This property controls if the Sentry CLI is to be used at all.  Setting false will disable all Sentry CLI usage. -->
      <UseSentryCLI Condition="'$(UseSentryCLI)' == '' And ('$(SentryUploadSymbols)' == 'true' Or '$(SentryUploadSources)' == 'true' Or $(SentryUploadAndroidProguardMapping) == 'true')">true</UseSentryCLI>
    </PropertyGroup>
  </Target>

  <Target Name="PrepareSentryCLI" AfterTargets="CheckSentryCLI" Condition="'$(UseSentryCLI)' == 'true'">
    <!--
      If Sentry was added via PackageReference, PkgSentry will already be set to the path of the Sentry nuget package.
      However, older-style projects using packages.config will need us to resolve the path manually.
    -->
    <PropertyGroup Condition="'$(SentryCLIDirectory)' == '' And '$(PkgSentry)' == ''">
      <PkgSentry Condition="$([System.String]::new('%(Reference.Identity)').StartsWith('Sentry,'))">$([System.IO.Directory]::GetParent(%(Reference.HintPath)).Parent.Parent.FullName)</PkgSentry>
    </PropertyGroup>

    <!-- Sentry CLI comes from the Sentry Nuget package when installed. -->
    <PropertyGroup Condition="'$(SentryCLIDirectory)' == '' And '$(PkgSentry)' != ''">
      <SentryCLIDirectory>$(PkgSentry)\tools\</SentryCLIDirectory>
    </PropertyGroup>

    <!--
      Choose the correct Sentry CLI executable depending on OS platform and architecture.
      For Windows on Arm64, we'll use the X64 build for now (which should run via emulation).
      Switch to a Windows Arm64 build when available. See https://github.com/getsentry/sentry-cli/issues/1426
    -->
    <PropertyGroup Condition="'$(SentryCLI)' == '' And '$(SentryCLIDirectory)' != ''">
      <_OSArchitecture>$([System.Runtime.InteropServices.RuntimeInformation]::OSArchitecture)</_OSArchitecture>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('OSX')) And $(_OSArchitecture) == 'Arm64'">$(SentryCLIDirectory)sentry-cli-Darwin-arm64</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('OSX')) And $(_OSArchitecture) == 'X64'">$(SentryCLIDirectory)sentry-cli-Darwin-x86_64</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Linux')) And $(_OSArchitecture) == 'Arm64'">$(SentryCLIDirectory)sentry-cli-Linux-aarch64</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Linux')) And $(_OSArchitecture) == 'X86'">$(SentryCLIDirectory)sentry-cli-Linux-i686</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Linux')) And $(_OSArchitecture) == 'X64'">$(SentryCLIDirectory)sentry-cli-Linux-x86_64</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Windows')) And $(_OSArchitecture) == 'Arm64'">$(SentryCLIDirectory)sentry-cli-Windows-x86_64.exe</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Windows')) And $(_OSArchitecture) == 'X86'">$(SentryCLIDirectory)sentry-cli-Windows-i686.exe</SentryCLI>
      <SentryCLI Condition="$([MSBuild]::IsOSPlatform('Windows')) And $(_OSArchitecture) == 'X64'">$(SentryCLIDirectory)sentry-cli-Windows-x86_64.exe</SentryCLI>
      <SentryCLI Condition="!Exists('$(SentryCLI)')"/>
    </PropertyGroup>

    <!--
      The Sentry configuration can be set manually in MSBuild properties if desired.
      Otherwise the default configuration will be used, as reported by "sentry-cli info".
      The defaults can be set either via config file, or environment variables, per: https://docs.sentry.io/product/cli/configuration/
    -->
    <PropertyGroup>
      <SentryCLIBaseOptions Condition="'$(SentryApiKey)' != ''">$(SentryCLIBaseOptions) --api-key $(SentryApiKey)</SentryCLIBaseOptions>
      <SentryCLIBaseOptions Condition="'$(SentryAuthToken)' != ''">$(SentryCLIBaseOptions) --auth-token $(SentryAuthToken)</SentryCLIBaseOptions>
      <SentryCLIBaseOptions Condition="'$(SentryUrl)' != ''">$(SentryCLIBaseOptions) --url $(SentryUrl)</SentryCLIBaseOptions>
      <SentryCLIBaseCommand>&quot;$(SentryCLI)&quot;</SentryCLIBaseCommand>
      <SentryCLIBaseCommand Condition="'$(SentryCLIBaseOptions.Trim())' != ''">$(SentryCLIBaseCommand) $(SentryCLIBaseOptions.Trim())</SentryCLIBaseCommand>

      <SentryCLIUploadOptions Condition="'$(SentryOrg)' != ''">$(SentryCLIUploadOptions) --org $(SentryOrg)</SentryCLIUploadOptions>
      <SentryCLIUploadOptions Condition="'$(SentryProject)' != ''">$(SentryCLIUploadOptions) --project $(SentryProject)</SentryCLIUploadOptions>
      <SentryCLIDebugFilesUploadCommand>$(SentryCLIBaseCommand) debug-files upload</SentryCLIDebugFilesUploadCommand>
      <SentryCLIDebugFilesUploadCommand Condition="'$(SentryCLIUploadOptions.Trim())' != ''">$(SentryCLIDebugFilesUploadCommand) $(SentryCLIUploadOptions.Trim())</SentryCLIDebugFilesUploadCommand>
      <SentryCLIProGuardMappingUploadCommand>$(SentryCLIBaseCommand) upload-proguard</SentryCLIProGuardMappingUploadCommand>
      <SentryCLIProGuardMappingUploadCommand Condition="'$(SentryCLIUploadOptions.Trim())' != ''">$(SentryCLIProGuardMappingUploadCommand) $(SentryCLIUploadOptions.Trim())</SentryCLIProGuardMappingUploadCommand>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SentryCLI)' != ''">
      <_SentryCLICommand>$(SentryCLIBaseCommand) info</_SentryCLICommand>
      <_SentryCLICommand Condition="'$(SentryOrg)' != '' And '$(SentryProject)' != ''">$(_SentryCLICommand) --no-defaults</_SentryCLICommand>
    </PropertyGroup>
    <Exec Condition="'$(SentryCLI)' != ''" Command="$(_SentryCLICommand)" IgnoreExitCode="true" ConsoleToMsBuild="true" StandardOutputImportance="Low">
      <Output TaskParameter="ExitCode" PropertyName="_SentryCLIExitCode" />
      <Output TaskParameter="ConsoleOutput" PropertyName="_SentryCLIOutput" />
    </Exec>

    <PropertyGroup Condition="'$(_SentryCLIExitCode)' != '0'">
      <_SentryCLIRequestFailed Condition="$(_SentryCLIOutput.Contains('API request failed'))">true</_SentryCLIRequestFailed>
    </PropertyGroup>
    <Warning Condition="'$(_SentryCLIRequestFailed)' != ''"
      Text="Sentry API request failed.  Either the authentication info is invalid, or the Sentry server could not be reached." />

    <Message Importance="High" Condition="'$(_SentryCLIExitCode)' != '0' And '$(_SentryCLIRequestFailed)' == ''"
      Text="The Sentry CLI is not fully configured with authentication, organization, and project." />
    <PropertyGroup Condition="'$(_SentryCLIExitCode)' != '0'">
      <SentryCLI />
    </PropertyGroup>

    <Warning Condition="'$(SentryUploadSources)' == 'true' And '$(EmbedAllSources)' == 'true'"
      Text="Both SentryUploadSources and EmbedAllSources are enabled.  Disabling SentryUploadSources." />
    <PropertyGroup Condition="'$(SentryUploadSources)' == 'true' And '$(EmbedAllSources)' == 'true'">
      <SentryUploadSources>false</SentryUploadSources>
    </PropertyGroup>

  </Target>

  <!-- Upload symbols to Sentry after the build. -->
  <Target Name="UploadSymbolsToSentry" AfterTargets="Build;Publish" DependsOnTargets="PrepareSentryCLI"
    Condition="'$(SentryUploadSymbols)' == 'true' And '$(SentryCLI)' != ''">

    <Message Importance="High"
      Text="Preparing to upload debug symbols to Sentry for $(MSBuildProjectName) ($(Configuration)/$(TargetFramework))" />

    <ItemGroup>
      <SentryCLIUploadSymbolType Include="pdb" />
      <SentryCLIUploadSymbolType Include="portablepdb" />
      <SentryCLIUploadSymbolType Include="dsym" Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'" />
      <SentryCLIUploadSymbolType Include="dsym" Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'" />
    </ItemGroup>

    <Exec Command="$(SentryCLIDebugFilesUploadCommand) @(SentryCLIUploadSymbolType -> '-t %(Identity)', ' ') $(OutputPath)" IgnoreExitCode="true" ContinueOnError="WarnAndContinue">
      <Output TaskParameter="ExitCode" PropertyName="_SentryCLIExitCode" />
    </Exec>

    <Warning Condition="'$(_SentryCLIExitCode)' != '0'" Text="Sentry CLI could not upload debug files." />

  </Target>

  <!-- Upload sources to Sentry after the build. -->
  <Target Name="UploadSourcesToSentry" AfterTargets="Build;UploadSymbolsToSentry" DependsOnTargets="PrepareSentryCLI"
    Condition="'$(SentryUploadSources)' == 'true' And '$(SentryCLI)' != ''">

    <Message Importance="High" Condition="'$(SentryUploadSources)' == 'true'"
      Text="Preparing to upload sources to Sentry for $(MSBuildProjectName) ($(Configuration)/$(TargetFramework))" />

    <PropertyGroup>
      <_SentryDebugInfoFile>@(IntermediateAssembly->'$(IntermediateOutputPath)%(FileName).pdb')</_SentryDebugInfoFile>
      <_SentryDebugInfoFile Condition="!Exists('$(_SentryDebugInfoFile)')">@(IntermediateAssembly->'$(IntermediateOutputPath)%(FileName)%(Extension)')</_SentryDebugInfoFile>
      <_SentrySourceBundle>@(IntermediateAssembly->'$(IntermediateOutputPath)%(FileName).src.zip')</_SentrySourceBundle>
    </PropertyGroup>

    <Exec Command="&quot;$(SentryCLI)&quot; debug-files bundle-sources $(_SentryDebugInfoFile)" IgnoreExitCode="true" ContinueOnError="WarnAndContinue" />
    <Exec Command="$(SentryCLIDebugFilesUploadCommand) $(_SentrySourceBundle)" IgnoreExitCode="true" ContinueOnError="WarnAndContinue" Condition="Exists('$(_SentrySourceBundle)')">
      <Output TaskParameter="ExitCode" PropertyName="_SentryCLIExitCode" />
    </Exec>

    <Warning Condition="'$(_SentryCLIExitCode)' != '0'" Text="Sentry CLI could not upload sources." />

  </Target>

  <!-- Upload Android Proguard mapping file to Sentry after the build. -->
  <Target Name="UploadAndroidProguardMappingFileToSentry" AfterTargets="Build"
          Condition="'$(SentryUploadAndroidProguardMapping)' == 'true' And '$(AndroidProguardMappingFile)' != ''">

    <Message Importance="High" Text="Preparing to upload Android Proguard mapping to Sentry for '$(MSBuildProjectName)': $(AndroidProguardMappingFile))" />

    <Exec Command="$(SentryCLIProGuardMappingUploadCommand) $(AndroidProguardMappingFile)" IgnoreExitCode="true" ContinueOnError="WarnAndContinue">
      <Output TaskParameter="ExitCode" PropertyName="_SentryCLIExitCode" />
    </Exec>
    <Warning Condition="'$(_SentryCLIExitCode)' != '0'" Text="Sentry CLI could not upload proguard mapping." />

  </Target>
</Project>
