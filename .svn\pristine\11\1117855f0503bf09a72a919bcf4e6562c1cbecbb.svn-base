﻿using OCRTools.Common;
using System;
using System.Data;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;
using System.Xml;

namespace OCRTools
{
    internal class CommonUpdate
    {
        private static Thread _updateThread;

        private static bool _isOnUpdate;
        public static bool isAlertUpdate;

        public static bool IsAutoCheckUpdate { get; set; } = true;

        public static void InitUpdate()
        {
            if (_updateThread != null)
                try
                {
                    _updateThread.Abort();
                    _updateThread = null;
                }
                catch
                {
                }

            try
            {
                InitUpdateService();
            }
            catch
            {
            }
        }

        private static void InitUpdateService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopHours",
                DateValue = (int)Math.Max(CommonSetting.自动更新间隔, 1),
                IsExecFirst = CommonSetting.启动时检查更新,
                Param = CommonString.UpdateFileUrl,
                DtNow = CommonString.DtNowDate
            };
            _updateThread = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
            _updateThread.Start();
        }

        public static bool Install(string url, DateTime dtNowDate, string desc, string appName, string appPath, bool isUpdateMode, bool isNeedUnZip, bool isAutoStart, bool isNeedClearFolder, FormUpdate updateForm, bool isDialog = false)
        {
            var result = false;
            UpdateEntity updateEntity = null;
            if (IsHasNew(url, dtNowDate, desc, ref updateEntity))
            {
                result = true;
                if (isUpdateMode)
                    CommonMethod.ShowHelpMsg("发现新版本,更新前请关闭360等杀毒软件，或添加信任，避免被误杀！");
                var form = ControlExtension.GetMetroForm();
                if (updateForm == null || updateForm.IsDisposed)
                {
                    updateForm = new FormUpdate
                    {
                        Icon = form.Icon,
                        Theme = form.Theme,
                        Style = form.Style,
                        StyleManager = form.StyleManager,
                        UpdateInfo = updateEntity,
                        TopMost = true,
                        StartPosition = FormStartPosition.CenterScreen,
                        AppName = appName,
                        AppPath = appPath,
                        IsUpdateMode = isUpdateMode,
                        IsNeedUnZip = isNeedUnZip,
                        IsCanUserUpdate = isUpdateMode,
                        IsAutoStart = isAutoStart,
                        IsNeedClearFolder = isNeedClearFolder
                    };
                }
                CommonMethod.DetermineCall(FrmMain.FrmTool, delegate
                {
                    if (isDialog)
                    {
                        updateForm.ShowDialog();
                    }
                    else
                    {
                        updateForm.Show();
                    }
                });
            }

            return result;
        }

        public static void UpdateMethod(bool isUserUpdate, DateTime dtDate, string url)
        {
            try
            {
                if (_isOnUpdate)
                {
                    if (isAlertUpdate) CommonMethod.ShowHelpMsg("正在检查更新中，请稍候重试！");
                    return;
                }

                var isCheckUpdate = isUserUpdate || IsAutoCheckUpdate;
                if (!isCheckUpdate)
                {
                    return;
                }

                _isOnUpdate = true;
                if (!Install(url, dtDate, Application.ProductName, Application.ProductName, Application.ExecutablePath, true, false, false, false, FrmMain.FormUpdate))
                {
                    if (isAlertUpdate)
                    {
                        isAlertUpdate = false;
                        CommonMethod.ShowHelpMsg(string.Format("当前版本:V{0}，已经是最新版本！", CommonString.StrNowVersion));
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("检查更新错误", oe);
            }
            finally
            {
                _isOnUpdate = false;
            }
        }

        internal static bool IsHasNew(string url, DateTime dtNowDate, string desc, ref UpdateEntity updateNew)
        {
            var result = false;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    updateNew = LoadVersionInfo(url, desc);
                    if (updateNew != null)
                        result = IsHasNewVersion(dtNowDate, updateNew);
                }
            }
            catch
            {
            }

            return result;
        }

        private static bool IsHasNewVersion(DateTime dtDate, UpdateEntity updateNew)
        {
            var result = dtDate < updateNew?.dtNewDate;
            return result;
        }

        /// <summary>
        ///     将Xml内容字符串转换成DataSet对象
        /// </summary>
        /// <param name="xmlStr"></param>
        /// <returns></returns>
        private static DataSet CXmlToDataSet(string xmlStr)
        {
            if (string.IsNullOrEmpty(xmlStr)) return null;
            StringReader strStream = null;
            XmlTextReader xmlrdr = null;
            try
            {
                var ds = new DataSet();
                //读取字符串中的信息
                strStream = new StringReader(xmlStr);
                //获取StrStream中的数据
                xmlrdr = new XmlTextReader(strStream);
                //ds获取Xmlrdr中的数据                
                ds.ReadXml(xmlrdr);
                return ds;
            }
            finally
            {
                //释放资源
                if (xmlrdr != null)
                {
                    xmlrdr.Close();
                    strStream.Close();
                    strStream.Dispose();
                }
            }
        }

        private static UpdateEntity LoadVersionInfo(string url, string desc)
        {
            UpdateEntity updateNew = null;

            if (url.Contains(".zip"))
            {
                try
                {
                    DateTime newDate;
                    using (var client = new WebClient() { Proxy = null })
                    {
                        client.OpenRead(url);
                        newDate = DateTime.Parse(client.ResponseHeaders["Last-Modified"]);
                    }

                    updateNew = new UpdateEntity
                    {
                        strNewVersion = "*******",
                        dtNewDate = newDate.Date,
                        strContext = desc,
                        strURL = url,
                        strFullURL = url,
                        IsForceUpdate = false
                    };
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }
            else
            {
                var strUpdate = WebClientExt.GetHtml(url + "?t=" + DateTime.Now.Ticks, 10);
                try
                {
                    var dsTemp = CXmlToDataSet(strUpdate);
                    if (dsTemp != null)
                    {
                        if (dsTemp.Tables.Count > 0 && dsTemp.Tables[0].Rows.Count > 0)
                            updateNew = new UpdateEntity
                            {
                                strNewVersion = dsTemp.Tables[0].Rows[0]["strNowVersion"].ToString(),
                                dtNewDate = DateTime.Parse(dsTemp.Tables[0].Rows[0]["dtNowDate"].ToString()),
                                strContext = dsTemp.Tables[0].Rows[0]["strContext"].ToString()
                                    .Replace("|", Environment.NewLine),
                                strURL = dsTemp.Tables[0].Rows[0]["strURL"].ToString(),
                                strFullURL = dsTemp.Tables[0].Rows[0]["strFullURL"].ToString(),
                                IsForceUpdate =
                                    BoxUtil.GetBooleanFromObject(dsTemp.Tables[0].Rows[0]["IsNowForce"].ToString(), false)
                            };
                        dsTemp.Dispose();
                    }
                }
                catch
                {
                }
            }

            return updateNew;
        }

        private static void HeadRequest(string url)
        {
        }
    }

    internal class UpdateEntity
    {
        public string strNewVersion { get; set; }
        public DateTime dtNewDate { get; set; }
        public string strContext { get; set; }
        public string strURL { get; set; }
        public string strFullURL { get; set; }
        public bool IsForceUpdate { get; set; }
    }
}