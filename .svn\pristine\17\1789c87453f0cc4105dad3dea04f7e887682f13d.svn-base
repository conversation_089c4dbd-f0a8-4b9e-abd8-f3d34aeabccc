﻿using System.Drawing;

namespace OCRTools.Colors
{
    public enum ThemeOption
    {
        白色 = 0,

        黑色 = 1
        //Custom = 2
    }

    public static class CommonThemes
    {
        /// <summary>
        ///     The default light theme.
        /// </summary>
        public static Theme LightTheme = new Theme
        {
            Background = Color.White,
            TickColor = Color.Black,
            LengthLabelColor = Color.Green,
            CenterLineColor = Color.Blue,
            MouseLineColor = Color.Orange,
            ThirdsLinesColor = Color.DeepSkyBlue,
            GoldenLineColor = Color.Gold,
            CustomLinesColor = Color.Red
        };

        /// <summary>
        ///     The default dark theme.
        /// </summary>
        public static Theme DarkTheme = new Theme
        {
            Background = Color.FromArgb(16, 16, 16),
            TickColor = Color.White,
            LengthLabelColor = Color.White,
            CenterLineColor = Color.Fuchsia,
            MouseLineColor = Color.Orange,
            ThirdsLinesColor = Color.DarkTurquoise,
            GoldenLineColor = Color.DarkGoldenrod,
            CustomLinesColor = Color.OrangeRed
        };
    }
}