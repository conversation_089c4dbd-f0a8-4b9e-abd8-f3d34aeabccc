<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>OcrLib</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>net461</TargetFramework>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>Preview</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <PackageReference Include="clipper_standard" Version="0.0.1" />
    <PackageReference Include="Emgu.CV" Version="4.5.4.4788" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.10.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Core" />
  </ItemGroup>
</Project>