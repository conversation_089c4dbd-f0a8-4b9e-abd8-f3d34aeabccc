using System.IO;

namespace ExcelLibrary.BinaryFileFormat
{
	public class LABELSST : CellValue
	{
		public int SSTIndex;

		public LABELSST(Record record)
			: base(record)
		{
		}

		public LABELSST()
		{
			Type = 253;
		}

		public override void Decode()
		{
			MemoryStream input = new MemoryStream(Data);
			BinaryReader binaryReader = new BinaryReader(input);
			RowIndex = binaryReader.ReadUInt16();
			ColIndex = binaryReader.ReadUInt16();
			XFIndex = binaryReader.ReadUInt16();
			SSTIndex = binaryReader.ReadInt32();
		}

		public override void Encode()
		{
			MemoryStream memoryStream = new MemoryStream();
			BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
			binaryWriter.Write(RowIndex);
			binaryWriter.Write(ColIndex);
			binaryWriter.Write(XFIndex);
			binaryWriter.Write(SSTIndex);
			Data = memoryStream.ToArray();
			Size = (ushort)Data.Length;
			base.Encode();
		}
	}
}
