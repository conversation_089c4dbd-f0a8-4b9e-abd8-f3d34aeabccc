﻿using System;
using System.Collections.Generic;
using System.Drawing;

namespace OCRTools.Units
{
    /// <summary>
    ///     A class that converts to and from pixel values.
    /// </summary>
    public class UnitConverter
    {
        public static Dictionary<MeasuringUnit, string> UnitStrings = new Dictionary<MeasuringUnit, string>
        {
            {MeasuringUnit.像素, "px"},
            {MeasuringUnit.厘米, "cm"},
            {MeasuringUnit.英寸, "in"},
            {MeasuringUnit.点, "pt"},
            {MeasuringUnit.百分比, "%"}
        };

        private readonly Func<float, int, float, float> _fromPixelConverter;

        private readonly Func<float, int, float, float> _toPixelConverter;

        public UnitConverter(MeasuringUnit unit, Size screenSize, float dpi)
        {
            Unit = unit;
            ScreenSize = screenSize;
            Dpi = dpi;
            _toPixelConverter = GetToPixelConverter(unit);
            _fromPixelConverter = GetFromPixelConverter(unit);
        }

        /// <summary>
        ///     The measuring unit this converter converts to/ from.
        /// </summary>
        public MeasuringUnit Unit { get; }

        /// <summary>
        ///     The size of the screen.
        /// </summary>
        public Size ScreenSize { get; set; }

        /// <summary>
        ///     The screen DPI used for conversion.
        /// </summary>
        public float Dpi { get; set; }

        /// <summary>
        ///     The string symbol of the unit this converter converts to/ from.
        /// </summary>
        public string UnitString => UnitStrings[Unit];

        /// <summary>
        ///     Converts the given value to a pixel value.
        /// </summary>
        /// <param name="value">A value in the unit this converter converts to/ from.</param>
        /// <returns>The value converted to pixels.</returns>
        public float ConvertToPixel(float value, bool vertical)
        {
            return _toPixelConverter.Invoke(value, vertical ? ScreenSize.Height : ScreenSize.Width, Dpi);
        }

        /// <summary>
        ///     Converts a given pixel value to the defined unit.
        /// </summary>
        /// <param name="value">A pixel value.</param>
        /// <returns>The value converted to the unit this converter converts to.</returns>
        public float ConvertFromPixel(float value, bool vertical)
        {
            return _fromPixelConverter.Invoke(value, vertical ? ScreenSize.Height : ScreenSize.Width, Dpi);
        }

        /// <summary>
        ///     Converts a given marker from pixels to the defined unit.
        /// </summary>
        /// <param name="marker">The marker to be converted.</param>
        /// <returns>The value converted to the unit this converter converts to.</returns>
        public float ConvertFromPixel(RulerMarker marker)
        {
            return ConvertFromPixel(marker.Value, marker.Vertical);
        }

        /// <summary>
        ///     Returns a function that converts the given measuring unit into pixels.
        /// </summary>
        private static Func<float, int, float, float> GetToPixelConverter(MeasuringUnit unit)
        {
            switch (unit)
            {
                case MeasuringUnit.英寸:
                    return (v, _, dpi) => v * dpi;
                case MeasuringUnit.点:
                    return (v, _, dpi) => v / 72.0f * dpi;
                case MeasuringUnit.厘米:
                    return (v, _, dpi) => v / 2.54f * dpi;
                case MeasuringUnit.百分比:
                    return (v, total, _) => v / 100.0f * total;
                default:
                    return (v, _, dpi) => v;
            }
        }

        /// <summary>
        ///     Returns a function that converts pixels into the given measuring unit.
        /// </summary>
        private static Func<float, int, float, float> GetFromPixelConverter(MeasuringUnit unit)
        {
            switch (unit)
            {
                case MeasuringUnit.英寸:
                    return (v, _, dpi) => v / dpi;
                case MeasuringUnit.点:
                    return (v, _, dpi) => v * 72.0f / dpi;
                case MeasuringUnit.厘米:
                    return (v, _, dpi) => v * 2.54f / dpi;
                case MeasuringUnit.百分比:
                    return (v, total, _) => v / total * 100.0f;
                default:
                    return (v, _, dpi) => v;
            }
        }
    }
}